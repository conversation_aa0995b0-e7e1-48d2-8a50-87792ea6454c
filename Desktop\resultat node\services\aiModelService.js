const AIModel = require('../models/AIModel');

async function analyzeDiagram(diagramData) {
  try {
    return await AIModel.analyzeDiagram(diagramData);
  } catch (error) {
    console.error('Error analyzing diagram:', error);
    throw error;
  }
}

async function generateCodeDiagram(diagramData) {
  try {
    return await AIModel.generateCodeDiagram(diagramData);
  } catch (error) {
    console.error('Error generating code from diagram:', error);
    throw error;
  }
}

module.exports = {
  analyzeDiagram,
  generateCodeDiagram
};