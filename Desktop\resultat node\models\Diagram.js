const { sql } = require('../config/db');

class Diagram {
  constructor(name, type, data, userId, id = null) {
    this.name = name;
    this.type = type;
    this.data = data;
    this.userId = userId;
    this.id = id;
  }

  async save() {
    try {
      const pool = await sql.connect(require('../config/db').config);
      const request = pool.request();

      request.input('name', sql.NVarChar, this.name);
      request.input('type', sql.NVarChar, this.type);
      request.input('data', sql.NVarChar, this.data);
      request.input('userId', sql.Int, this.userId);

      const result = await request.query(
        `INSERT INTO Diagrams (name, type, data, userId) 
         OUTPUT INSERTED.id 
         VALUES (@name, @type, @data, @userId)`
      );

      this.id = result.recordset[0].id;
      sql.close();
      return this;
    } catch (err) {
      console.error('Error saving diagram:', err);
      throw err;
    }
  }

  static async validate(diagramData) {
    // Simulate diagram validation
    return diagramData != null && diagramData.length > 0;
  }
}

module.exports = Diagram;