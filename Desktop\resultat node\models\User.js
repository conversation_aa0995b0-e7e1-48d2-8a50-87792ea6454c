const { sql } = require('../config/db');
const bcrypt = require('bcrypt');

class User {
  constructor(name, email, password, role = 'user', id = null) {
    this.name = name;
    this.email = email;
    this.password = password;
    this.role = role;
    this.id = id;
  }

  static async findByEmail(email) {
    try {
      const pool = await sql.connect(require('../config/db').config);
      const request = pool.request();
      request.input('email', sql.NVarChar, email);
      const result = await request.query('SELECT * FROM Users WHERE email = @email');
      sql.close();

      if (result.recordset.length > 0) {
        const user = result.recordset[0];
        return new User(user.name, user.email, user.password, user.role, user.id);
      }

      return null;
    } catch (err) {
      console.error('Error finding user by email:', err);
      throw err;
    }
  }

  async save() {
    try {
      const pool = await sql.connect(require('../config/db').config);
      const request = pool.request();

      const hashedPassword = await bcrypt.hash(this.password, 10);

      request.input('name', sql.NVarChar, this.name);
      request.input('email', sql.NVarChar, this.email);
      request.input('password', sql.NVarChar, hashedPassword);
      request.input('role', sql.NVarChar, this.role);

      const result = await request.query(
        `INSERT INTO Users (name, email, password, role) 
         OUTPUT INSERTED.id 
         VALUES (@name, @email, @password, @role)`
      );

      this.id = result.recordset[0].id;
      sql.close();
      return this;
    } catch (err) {
      console.error('Error saving user:', err);
      throw err;
    }
  }

  async saveDiagram(diagram) {
    // Logic to save diagram
    console.log(`Diagram saved by user ${this.email}: ${diagram}`);
  }

  static async getAll() {
    try {
        const pool = await sql.connect(require('../config/db').config);
        const result = await pool.request().query('SELECT * FROM Users');
        sql.close();
        return result.recordset.map(user => new User(user.name, user.email, user.password, user.role, user.id));
    } catch (err) {
        console.error('Error getting all users:', err);
        throw err;
    }
  }
}

module.exports = User;