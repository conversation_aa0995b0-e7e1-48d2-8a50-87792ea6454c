const diagramService = require('../services/diagramService');

async function uploadDiagram(req, res, next) {
  try {
    const { name, type, data, userId } = req.body;
    const diagram = await diagramService.uploadDiagram(name, type, data, userId);
    res.status(201).json(diagram);
  } catch (error) {
    next(error);
  }
}

async function validateDiagram(req, res, next) {
  try {
    const diagramData = req.body.diagram;
    const isValid = await diagramService.validateDiagram(diagramData);
    res.json({ isValid });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  uploadDiagram,
  validateDiagram
};