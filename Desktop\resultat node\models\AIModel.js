const { sql } = require('../config/db');

class AIModel {
    constructor(ModelName, id) {
        this.ModelName = ModelName;
        this.id = id;
    }

    static async analyzeDiagram(diagram) {
        // Simulate AI analysis
        return `Analysis result for diagram: ${diagram}`;
    }

    static async generateCodeDiagram(diagram) {
        // Simulate code generation
        return `Generated code for diagram: ${diagram}`;
    }

    static async getAll() {
        try {
            const pool = await sql.connect(require('../config/db').config);
            const result = await pool.request().query('SELECT * FROM AIModels');
            sql.close();
            return result.recordset.map(record => new AIModel(record.ModelName, record.id));
        } catch (err) {
            console.error('Error getting AI Models:', err);
            throw err;
        }
    }
}

module.exports = AIModel;