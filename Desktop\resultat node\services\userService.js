const User = require('../models/User');
const bcrypt = require('bcrypt');

async function signup(name, email, password) {
  try {
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw new Error('Email already exists');
    }

    const user = new User(name, email, password);
    return await user.save();
  } catch (error) {
    console.error('Error during signup:', error);
    throw error;
  }
}

async function login(email, password) {
  try {
    const user = await User.findByEmail(email);
    if (!user) {
      return null;
    }

    const passwordMatch = await bcrypt.compare(password, user.password);
    if (!passwordMatch) {
      return null;
    }

    return {
      id: user.id,
      email: user.email, 
      role: user.role
    };
  } catch (error) {
    console.error('Error during login:', error);
    throw error;
  }
}

module.exports = {
  signup,
  login
};