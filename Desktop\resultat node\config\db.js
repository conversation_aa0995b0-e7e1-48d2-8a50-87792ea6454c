const sql = require('mssql');

const config = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'yourStrong(!)Password',
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'DiagramDB',
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: false, 
    trustServerCertificate: true 
  }
};

async function connectDB() {
  try {
    await sql.connect(config);
    console.log('Connected to the database');
  } catch (err) {
    console.error('Database connection error:', err);
    throw err;
  }
}

module.exports = { connectDB, sql };