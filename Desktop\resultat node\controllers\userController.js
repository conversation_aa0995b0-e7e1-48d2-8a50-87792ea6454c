const userService = require('../services/userService');
const jwtUtils = require('../utils/jwtUtils');

async function signup(req, res, next) {
  try {
    const { name, email, password } = req.body;
    const user = await userService.signup(name, email, password);
    res.status(201).json({ message: 'User created successfully', user });
  } catch (error) {
    next(error);
  }
}

async function login(req, res, next) {
  try {
    const { email, password } = req.body;
    const user = await userService.login(email, password);

    if (user) {
      const token = jwtUtils.generateToken(user);
      res.json({ message: 'Login successful', token });
    } else {
      res.status(401).json({ message: 'Invalid credentials' });
    }
  } catch (error) {
    next(error);
  }
}

module.exports = {
  signup,
  login
};