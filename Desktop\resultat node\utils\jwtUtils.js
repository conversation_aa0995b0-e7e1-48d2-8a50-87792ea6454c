const jwt = require('jsonwebtoken');

const secretKey = process.env.JWT_SECRET || 'your_jwt_secret';
const tokenExpiration = '1h';

function generateToken(user) {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role
  };

  return jwt.sign(payload, secretKey, { expiresIn: tokenExpiration });
}

function verifyToken(token, callback) {
  jwt.verify(token, secretKey, callback);
}

module.exports = {
  generateToken,
  verifyToken
};