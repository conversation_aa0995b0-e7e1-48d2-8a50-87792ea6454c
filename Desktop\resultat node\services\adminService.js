const User = require('../models/User');
const AIModel = require('../models/AIModel');

async function getAllUsers() {
  try {
    return await User.getAll();
  } catch (error) {
    console.error('Error getting all users in admin service:', error);
    throw error;
  }
}

async function getAllAIModels() {
  try {
    return await AIModel.getAll();
  } catch (error) {
    console.error('Error getting all AI models in admin service:', error);
    throw error;
  }
}

async function saveUser(userData) {
    try {
        const user = new User(userData.name, userData.email, userData.password, userData.role);
        return await user.save();
    } catch (error) {
        console.error('Error saving user in admin service:', error);
        throw error;
    }
}

module.exports = {
  getAllUsers,
  getAllAIModels,
  saveUser
};