const aiModelService = require('../services/aiModelService');

async function analyzeDiagram(req, res, next) {
  try {
    const diagramData = req.body.diagram;
    const analysisResult = await aiModelService.analyzeDiagram(diagramData);
    res.json({ result: analysisResult });
  } catch (error) {
    next(error);
  }
}

async function generateCodeDiagram(req, res, next) {
  try {
    const diagramData = req.body.diagram;
    const generatedCode = await aiModelService.generateCodeDiagram(diagramData);
    res.json({ code: generatedCode });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  analyzeDiagram,
  generateCodeDiagram
};