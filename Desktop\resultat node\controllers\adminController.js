const adminService = require('../services/adminService');

async function manageUsers(req, res, next) {
  try {
    const users = await adminService.getAllUsers();
    res.json(users);
  } catch (error) {
    next(error);
  }
}

async function manageAIModels(req, res, next) {
  try {
    const models = await adminService.getAllAIModels();
    res.json(models);
  } catch (error) {
    next(error);
  }
}

async function saveUser(req, res, next) {
    try {
        const userData = req.body;
        const savedUser = await adminService.saveUser(userData);
        res.status(201).json(savedUser);
    } catch (error) {
        next(error);
    }
}

module.exports = {
  manageUsers,
  manageAIModels,
  saveUser
};