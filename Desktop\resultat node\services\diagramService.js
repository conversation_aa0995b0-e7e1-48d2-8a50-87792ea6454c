const Diagram = require('../models/Diagram');

async function uploadDiagram(name, type, data, userId) {
  try {
    const diagram = new Diagram(name, type, data, userId);
    return await diagram.save();
  } catch (error) {
    console.error('Error uploading diagram:', error);
    throw error;
  }
}

async function validateDiagram(diagramData) {
  try {
    return await Diagram.validate(diagramData);
  } catch (error) {
    console.error('Error validating diagram:', error);
    throw error;
  }
}

module.exports = {
  uploadDiagram,
  validateDiagram
};