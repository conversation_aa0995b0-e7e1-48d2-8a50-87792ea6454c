const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { authenticateToken } = require('../middleware/authMiddleware');

router.get('/users', authenticateToken, adminController.manageUsers);
router.get('/ai-models', authenticateToken, adminController.manageAIModels);
router.post('/users', authenticateToken, adminController.saveUser);

module.exports = router;