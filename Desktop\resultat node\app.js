const express = require('express');
const cors = require('cors');
const userRoutes = require('./routes/userRoutes');
const diagramRoutes = require('./routes/diagramRoutes');
const adminRoutes = require('./routes/adminRoutes');
const aiModelRoutes = require('./routes/aiModelRoutes');
const { errorHandler } = require('./middleware/errorHandler');

// Swagger
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Diagram2Code API',
      version: '1.0.0',
      description: 'Documentation Swagger pour les routes de Diagram2Code'
    },
    servers: [
      {
        url: `http://localhost:${port}`
      }
    ]
  },
  apis: ['./routes/*.js'], // ajoute les fichiers où les routes sont commentées avec Swagger
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Routes principales
app.use('/api/users', userRoutes);
app.use('/api/diagrams', diagramRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/ai-models', aiModelRoutes);

// Middleware de gestion d'erreur
app.use(errorHandler);

// Lancement du serveur
app.listen(port, () => {
  console.log(`✅ Server is running on http://localhost:${port}`);
  console.log(`📚 Swagger UI available at http://localhost:${port}/api-docs`);
});
